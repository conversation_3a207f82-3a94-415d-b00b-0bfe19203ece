package ai.areas.AncientPiratesTomb;

import java.util.concurrent.atomic.AtomicInteger;

import org.l2jmobius.gameserver.data.xml.SpawnData;
import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.spawns.SpawnGroup;
import org.l2jmobius.gameserver.model.spawns.SpawnTemplate;
import org.l2jmobius.gameserver.network.serverpackets.ExShowScreenMessage;
import org.l2jmobius.gameserver.util.Broadcast;

import ai.AbstractNpcAI;

public class AncientPiratesTomb extends AbstractNpcAI
{
	private static final int[]			ALL_MONSTERS					=
	{
		21939,																																// Pirate Janitor
		21940,																																// Pirate Trooper
		21941,																																// Pirate Shaman
		21942,																																// Pirate Shield-Bearer
		21943,																																// Ryurik
		21944,																																// Eric
		21945,																																// Leif
		21946																																// Gunhilde
	};
	private static final SpawnTemplate	GOLDBERG_STEWARD_LOC_1_SPAWN	= SpawnData.getInstance().getSpawnByName("GoldbergSteward_loc_1");
	private static final SpawnTemplate	GOLDBERG_STEWARD_LOC_2_SPAWN	= SpawnData.getInstance().getSpawnByName("GoldbergSteward_loc_2");
	private static final SpawnTemplate	GOLDBERG_STEWARD_LOC_3_SPAWN	= SpawnData.getInstance().getSpawnByName("GoldbergSteward_loc_3");
	private static final AtomicInteger	KILL_COUNTER					= new AtomicInteger();
	private static boolean				GOLDBERG_STEWARD_LOC_1_SPAWNED	= false;
	private static boolean				GOLDBERG_STEWARD_LOC_2_SPAWNED	= false;
	private static boolean				GOLDBERG_STEWARD_LOC_3_SPAWNED	= false;
	
	public AncientPiratesTomb()
	{
		addKillId(ALL_MONSTERS);
	}
	
	@Override
	public String onEvent(String event, Npc npc, Player player)
	{
		switch (event)
		{
			case "announce_steward":
			{
				Broadcast.toAllOnlinePlayers(new ExShowScreenMessage("Goldberg's Steward has appeared. Find and kill him to get Goldberg's Room Key", 2, 10000, 0, true, true));
				break;
			}
		}
		return super.onEvent(event, npc, player);
	}
	
	@Override
	public String onKill(Npc npc, Player killer, boolean isSummon)
	{
		// Kiểm tra nếu người chơi thuộc một party.
		if (killer.getParty() != null)
		{
			// Lấy danh sách tất cả thành viên trong party của người chơi.
			killer.getParty().getMembers().forEach(member ->
			{
				// Chỉ gửi thông điệp cho người chơi đã giết quái.
				if (member == killer)
				{
					member.sendMessage("Party kill count: " + KILL_COUNTER.incrementAndGet() + " /200");
					member.sendPacket(new ExShowScreenMessage("Party kill count: " + KILL_COUNTER.incrementAndGet() + " /200", 2, 10000, 0, true, true));
				}
			});
		}
		else
		{
			// Người chơi không thuộc party, tăng bộ đếm cho chính người chơi.
			killer.sendMessage("Killer count: " + KILL_COUNTER.incrementAndGet() + " /200");
			killer.sendPacket(new ExShowScreenMessage("Killer count: " + KILL_COUNTER.incrementAndGet() + " /200", 2, 10000, 0, true, true));
		}
		// Kiểm tra nếu đã giết đủ 200 quái.
		if (KILL_COUNTER.get() >= 200)
		{
			// Đặt lại bộ đếm giết quái.
			KILL_COUNTER.set(0);
			// Chọn ngẫu nhiên một trong ba vị trí spawn.
			int randomLocation = getRandom(1, 3);
			switch (randomLocation)
			{
				case 1:
					if (!GOLDBERG_STEWARD_LOC_1_SPAWNED)
					{
						GOLDBERG_STEWARD_LOC_1_SPAWNED = true;
						startQuestTimer("announce_steward", 10000, null, null);
						GOLDBERG_STEWARD_LOC_1_SPAWN.getGroups().forEach(SpawnGroup::spawnAll);
					}
					break;
				case 2:
					if (!GOLDBERG_STEWARD_LOC_2_SPAWNED)
					{
						GOLDBERG_STEWARD_LOC_2_SPAWNED = true;
						startQuestTimer("announce_steward", 10000, null, null);
						GOLDBERG_STEWARD_LOC_2_SPAWN.getGroups().forEach(SpawnGroup::spawnAll);
					}
					break;
				case 3:
					if (!GOLDBERG_STEWARD_LOC_3_SPAWNED)
					{
						GOLDBERG_STEWARD_LOC_3_SPAWNED = true;
						startQuestTimer("announce_steward", 10000, null, null);
						GOLDBERG_STEWARD_LOC_3_SPAWN.getGroups().forEach(SpawnGroup::spawnAll);
					}
					break;
			}
		}
		return super.onKill(npc, killer, isSummon);
	}
	
	public static void main(String[] args)
	{
		new AncientPiratesTomb();
	}
}
